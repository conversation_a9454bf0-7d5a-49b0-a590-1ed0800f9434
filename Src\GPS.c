/**
  ******************************************************************************
  * @file    GPS.c
  * @brief   GPS module driver source file
  ******************************************************************************
  */

#include "GPS.h"

// Global variables
GPS_Data_t gps_data;
Course_Data_t course_data;
uint8_t gps_data_ready = 0;
uint8_t gps_new_data = 0;
char gps_nmea_buffer[256]; // Store NMEA sentences to avoid circular overwrite

// GPS module initialization
void GPS_Init(void)
{
    // Clear GPS data structure
    memset(&gps_data, 0, sizeof(GPS_Data_t));

    // Initialize with current RTC date/time instead of hardcoded defaults
    RTC_TimeTypeDef rtc_time;
    RTC_DateTypeDef rtc_date;
    HAL_RTC_GetTime(&hrtc, &rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &rtc_date, RTC_FORMAT_BIN);

    // Set GPS default values from current RTC
    gps_data.year = 2000 + rtc_date.Year;
    gps_data.month = rtc_date.Month;
    gps_data.day = rtc_date.Date;
    gps_data.hour = rtc_time.Hours;
    gps_data.minute = rtc_time.Minutes;
    gps_data.second = rtc_time.Seconds;

    // Clear NMEA buffer
    memset(gps_nmea_buffer, 0, sizeof(gps_nmea_buffer));

    // Initialize flags
    gps_data_ready = 0;
    gps_new_data = 0;

    // Initialize course data
    GPS_CourseInit();

    printf("GPS module initialized with RTC time: %04d-%02d-%02d %02d:%02d:%02d\r\n",
           gps_data.year, gps_data.month, gps_data.day,
           gps_data.hour, gps_data.minute, gps_data.second);
}

// Initialize course data
void GPS_CourseInit(void)
{
    // Clear course data structure
    memset(&course_data, 0, sizeof(Course_Data_t));

    // Initialize course history data
    for (int i = 0; i < COURSE_HISTORY_SIZE; i++) {
        course_data.course_history[i] = 0.0f;
    }

    course_data.history_count = 0;
    course_data.history_index = 0;
    course_data.last_valid_course = 0.0f;
    course_data.has_valid_course = 0;
}

// Process NMEA data
void GPS_Process(char *nmea_data)
{
    if (GPS_ParseNMEA(nmea_data, &gps_data)) {
        gps_data.updated = 1;
    }
}

// Parse NMEA data
uint8_t GPS_ParseNMEA(char *nmea_data, GPS_Data_t *gps_data)
{
    char *token;
    char buffer[128];
    uint8_t field_count = 0;
    uint8_t result = 0;

    // Check input parameter validity
    if (nmea_data == NULL || gps_data == NULL) {
        return 0;
    }

    // Copy data to buffer to avoid modifying original data
    strncpy(buffer, nmea_data, sizeof(buffer) - 1);
    buffer[sizeof(buffer) - 1] = '\0';

    // Get first field
    token = strtok(buffer, ",");
    if (token == NULL) {
        return 0;
    }

    // Parse GGA sentence - Global Positioning System fix data
    if (strcmp(token, "$GNGGA") == 0 || strcmp(token, "$GPGGA") == 0) {
        // Save original GGA sentence
        strncpy(gps_data->gga, nmea_data, sizeof(gps_data->gga) - 1);
        gps_data->gga[sizeof(gps_data->gga) - 1] = '\0';

        field_count = 1;
        while ((token = strtok(NULL, ",")) != NULL) {
            switch (field_count) {
                case 1: // Time
                    if (strlen(token) >= 6) {
                        gps_data->hour = (token[0] - '0') * 10 + (token[1] - '0');
                        gps_data->minute = (token[2] - '0') * 10 + (token[3] - '0');
                        gps_data->second = (token[4] - '0') * 10 + (token[5] - '0');
                        if (strlen(token) > 6 && token[6] == '.') {
                            gps_data->millisecond = atoi(&token[7]);
                        }
                    }
                    break;
                case 2: // Latitude
                    if (strlen(token) > 0) {
                        gps_data->latitude = atof(token);
                    }
                    break;
                case 3: // North/South hemisphere
                    if (strlen(token) > 0) {
                        gps_data->ns = token[0];
                    }
                    break;
                case 4: // Longitude
                    if (strlen(token) > 0) {
                        gps_data->longitude = atof(token);
                    }
                    break;
                case 5: // East/West hemisphere
                    if (strlen(token) > 0) {
                        gps_data->ew = token[0];
                    }
                    break;
                case 6: // Fix quality
                    if (strlen(token) > 0) {
                        gps_data->fix_quality = atoi(token);
                        if (gps_data->fix_quality > 0) {
                            gps_data->valid = 1;
                        } else {
                            gps_data->valid = 0;
                        }
                    }
                    break;
                case 7: // Number of satellites used
                    if (strlen(token) > 0) {
                        gps_data->satellites = atoi(token);
                    }
                    break;
                case 8: // Horizontal dilution of precision
                    if (strlen(token) > 0) {
                        gps_data->hdop = atof(token);
                    }
                    break;
                case 9: // Altitude
                    if (strlen(token) > 0) {
                        gps_data->altitude = atof(token);
                    }
                    break;
                default:
                    break;
            }
            field_count++;
        }

        // Calculate decimal degree format coordinates
        if (gps_data->latitude > 0 && gps_data->longitude > 0) {
            gps_data->latitude_decimal = GPS_ConvertToDecimalDegrees(gps_data->latitude, gps_data->ns);
            gps_data->longitude_decimal = GPS_ConvertToDecimalDegrees(gps_data->longitude, gps_data->ew);
        }

        result = 1;
    }
    // Parse RMC sentence - Recommended minimum positioning information
    else if (strcmp(token, "$GNRMC") == 0 || strcmp(token, "$GPRMC") == 0) {
        // Save original RMC sentence
        strncpy(gps_data->rmc, nmea_data, sizeof(gps_data->rmc) - 1);
        gps_data->rmc[sizeof(gps_data->rmc) - 1] = '\0';

        field_count = 1;
        while ((token = strtok(NULL, ",*")) != NULL) {
            switch (field_count) {
                case 1: // Time
                    if (strlen(token) >= 6) {
                        gps_data->hour = (token[0] - '0') * 10 + (token[1] - '0');
                        gps_data->minute = (token[2] - '0') * 10 + (token[3] - '0');
                        gps_data->second = (token[4] - '0') * 10 + (token[5] - '0');
                        if (strlen(token) > 6 && token[6] == '.') {
                            gps_data->millisecond = atoi(&token[7]);
                        }
                    }
                    break;
                case 2: // Status A=valid, V=invalid
                    if (strlen(token) > 0) {
                        gps_data->valid = (token[0] == 'A') ? 1 : 0;
                    }
                    break;
                case 3: // Latitude
                    if (strlen(token) > 0) {
                        gps_data->latitude = atof(token);
                    }
                    break;
                case 4: // North/South hemisphere
                    if (strlen(token) > 0) {
                        gps_data->ns = token[0];
                    }
                    break;
                case 5: // Longitude
                    if (strlen(token) > 0) {
                        gps_data->longitude = atof(token);
                    }
                    break;
                case 6: // East/West hemisphere
                    if (strlen(token) > 0) {
                        gps_data->ew = token[0];
                    }
                    break;
                case 7: // Speed (knots)
                    if (strlen(token) > 0) {
                        gps_data->speed = atof(token);
                    }
                    break;
                case 8: // Course (degrees)
                    if (strlen(token) > 0) {
                        gps_data->course = atof(token);
                    }
                    break;
                case 9: // Date (DDMMYY)
                    if (strlen(token) >= 6) {
                        // Parse date with validity check
                        uint8_t day = (token[0] - '0') * 10 + (token[1] - '0');
                        uint8_t month = (token[2] - '0') * 10 + (token[3] - '0');
                        uint16_t year = 2000 + (token[4] - '0') * 10 + (token[5] - '0');

                        // Only update GPS data when parsed date is valid
                        if (year >= 2020 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                            gps_data->day = day;
                            gps_data->month = month;
                            gps_data->year = year;
                        }
                        // If date is invalid, keep original values unchanged
                    }
                    break;
                default:
                    break;
            }
            field_count++;
        }

        // Calculate decimal degree format coordinates
        if (gps_data->latitude > 0 && gps_data->longitude > 0) {
            gps_data->latitude_decimal = GPS_ConvertToDecimalDegrees(gps_data->latitude, gps_data->ns);
            gps_data->longitude_decimal = GPS_ConvertToDecimalDegrees(gps_data->longitude, gps_data->ew);
        }

        result = 1;
    }

    // Parse GSA sentence - Satellite status
    else if (strcmp(token, "$GNGSA") == 0 || strcmp(token, "$GPGSA") == 0 || strcmp(token, "$BDGSA") == 0) {
        field_count = 1;
        while ((token = strtok(NULL, ",*")) != NULL) {
            // GSA sentence contains precision factor fields: PDOP, HDOP, VDOP
            if (field_count == 15) { // PDOP
                if (strlen(token) > 0) {
                    gps_data->pdop = atof(token);
                }
            } else if (field_count == 16) { // HDOP
                if (strlen(token) > 0) {
                    // Only update if GGA sentence didn't provide HDOP
                    if (gps_data->hdop == 0.0f) {
                        gps_data->hdop = atof(token);
                    }
                }
            } else if (field_count == 17) { // VDOP
                if (strlen(token) > 0) {
                    gps_data->vdop = atof(token);
                }
            }
            field_count++;
        }
        result = 1;
    }
    // Parse ZDA sentence - Date and time
    else if (strcmp(token, "$GNZDA") == 0 || strcmp(token, "$GPZDA") == 0) {
        field_count = 1;
        while ((token = strtok(NULL, ",*")) != NULL) {
            switch (field_count) {
                case 1: // Time
                    if (strlen(token) >= 6) {
                        gps_data->hour = (token[0] - '0') * 10 + (token[1] - '0');
                        gps_data->minute = (token[2] - '0') * 10 + (token[3] - '0');
                        gps_data->second = (token[4] - '0') * 10 + (token[5] - '0');
                        if (strlen(token) > 6 && token[6] == '.') {
                            gps_data->millisecond = atoi(&token[7]);
                        }
                    }
                    break;
                case 2: // Day
                    if (strlen(token) > 0) {
                        gps_data->day = atoi(token);
                    }
                    break;
                case 3: // Month
                    if (strlen(token) > 0) {
                        gps_data->month = atoi(token);
                    }
                    break;
                case 4: // Year
                    if (strlen(token) > 0) {
                        gps_data->year = atoi(token);
                    }
                    break;
                default:
                    break;
            }
            field_count++;
        }
        result = 1;
    }

    return result;
}

// Convert NMEA format coordinates to decimal degrees format
float GPS_ConvertToDecimalDegrees(float nmea_format, char direction)
{
    int degrees = (int)(nmea_format / 100);
    float minutes = nmea_format - degrees * 100;
    float decimal_degrees = degrees + minutes / 60.0f;

    // Southern and western hemispheres are negative
    if (direction == 'S' || direction == 'W') {
        decimal_degrees = -decimal_degrees;
    }

    return decimal_degrees;
}

// Print GPS information
void GPS_PrintInfo(GPS_Data_t *gps_data)
{
    if (gps_data == NULL) {
        return;
    }

    printf("\r\n===== GPS Information =====\r\n");

    // Time and date
    printf("Time: %02d:%02d:%02d.%03d (UTC)\r\n",
           gps_data->hour, gps_data->minute, gps_data->second, gps_data->millisecond);
    printf("Date: %04d-%02d-%02d\r\n",
           gps_data->year, gps_data->month, gps_data->day);

    // Position information
    if (gps_data->valid) {
        printf("Latitude: %d°%.5f' %c\r\n",
               (int)(gps_data->latitude / 100),
               gps_data->latitude - (int)(gps_data->latitude / 100) * 100,
               gps_data->ns);
        printf("Longitude: %d°%.5f' %c\r\n",
               (int)(gps_data->longitude / 100),
               gps_data->longitude - (int)(gps_data->longitude / 100) * 100,
               gps_data->ew);
        printf("Decimal coordinates: %.7f, %.7f\r\n",
               gps_data->latitude_decimal, gps_data->longitude_decimal);
        printf("Fix quality: %d\r\n", gps_data->fix_quality);
        printf("Satellites used: %d\r\n", gps_data->satellites);
        printf("PDOP position accuracy: %.1f\r\n", gps_data->pdop);
        printf("HDOP horizontal accuracy: %.1f\r\n", gps_data->hdop);
        printf("VDOP vertical accuracy: %.1f\r\n", gps_data->vdop);
        printf("Altitude: %.1f m\r\n", gps_data->altitude);

        float speed_kmh = gps_data->speed * 1.852f;
        printf("Speed: %.2f knots (%.2f km/h)\r\n", gps_data->speed, speed_kmh);

        // Determine course reliability based on speed
        if (speed_kmh >= COURSE_SPEED_THRESHOLD) {
            printf("Course: %.1f° [Reliable]\r\n", gps_data->course);
        } else {
            printf("Course: %.1f° [Unreliable - using historical value]\r\n", gps_data->course);
        }
    } else {
        printf("Position status: Invalid\r\n");
    }

    printf("=====================\r\n");
}

// Synchronize RTC clock
uint8_t GPS_SyncRTC(GPS_Data_t *gps_data)
{
    if (gps_data == NULL || !gps_data->valid) {
        printf("RTC sync failed: GPS data invalid\r\n");
        return 0;
    }

    // Smart GPS time sync: when GPS has positioning info, time is definitely valid
    // Check if time is valid
    uint8_t time_valid = (gps_data->hour <= 23 && gps_data->minute <= 59 && gps_data->second <= 59);
    if (!time_valid) {
        printf("RTC sync failed: GPS time format invalid %02d:%02d:%02d\r\n",
               gps_data->hour, gps_data->minute, gps_data->second);
        return 0;
    }

    // Check if date is valid
    uint8_t date_valid = (gps_data->year >= 2020 && gps_data->month >= 1 &&
                         gps_data->month <= 12 && gps_data->day >= 1 && gps_data->day <= 31);
    if (!date_valid) {
        printf("RTC sync skipped: GPS date invalid %04d-%02d-%02d, keeping RTC date\r\n",
               gps_data->year, gps_data->month, gps_data->day);

        // GPS date invalid but time valid, sync time only, keep RTC date
        RTC_TimeTypeDef rtc_time;
        RTC_DateTypeDef rtc_date;
        HAL_RTC_GetTime(&hrtc, &rtc_time, RTC_FORMAT_BIN);
        HAL_RTC_GetDate(&hrtc, &rtc_date, RTC_FORMAT_BIN);

        return RTC_SetDateTime(gps_data->hour, gps_data->minute, gps_data->second,
                              rtc_date.Date, rtc_date.Month, rtc_date.Year) == HAL_OK ? 1 : 0;
    }

    // GPS time and date both valid, perform complete sync
    if (RTC_SetDateTime(gps_data->hour, gps_data->minute, gps_data->second,
                        gps_data->day, gps_data->month, gps_data->year % 100) == HAL_OK) {
        printf("RTC synchronized with GPS time and date: %02d:%02d:%02d %04d-%02d-%02d\r\n",
               gps_data->hour, gps_data->minute, gps_data->second,
               gps_data->year, gps_data->month, gps_data->day);
        return 1;
    } else {
        printf("RTC sync failed: HAL error\r\n");
        return 0;
    }
}

// Get position string for data transmission
char* GPS_GetPositionString(GPS_Data_t *gps_data, char *buffer)
{
    if (gps_data == NULL || buffer == NULL || !gps_data->valid) {
        return NULL;
    }

    // Format position information as string, suitable for GM20 module transmission
    sprintf(buffer, "+%.5f+%.5f+%.1f+%d+%.2f",
            gps_data->latitude,
            gps_data->longitude,
            gps_data->altitude,
            gps_data->satellites,
            gps_data->hdop);

    return buffer;
}

// Process course data
float GPS_ProcessCourse(float course, float speed_kmh)
{
    // If speed is below threshold, use last valid course
    if (speed_kmh < COURSE_SPEED_THRESHOLD) {
        return course_data.has_valid_course ? course_data.last_valid_course : 0.0f;
    }

    // Speed above threshold, current course is valid
    course_data.has_valid_course = 1;
    course_data.last_valid_course = course;

    // Add to history data
    course_data.course_history[course_data.history_index] = course;
    course_data.history_index = (course_data.history_index + 1) % COURSE_HISTORY_SIZE;
    if (course_data.history_count < COURSE_HISTORY_SIZE) {
        course_data.history_count++;
    }

    // Return smoothed course
    return GPS_GetSmoothedCourse();
}

// Get smoothed course value
float GPS_GetSmoothedCourse(void)
{
    // If not enough history data, return last valid course directly
    if (course_data.history_count < 2) {
        return course_data.last_valid_course;
    }

    // Calculate course average (need special handling for angle circularity)
    float sin_sum = 0.0f;
    float cos_sum = 0.0f;

    for (int i = 0; i < course_data.history_count; i++) {
        // Convert angle to radians
        float angle_rad = course_data.course_history[i] * 3.14159f / 180.0f;
        sin_sum += sinf(angle_rad);
        cos_sum += cosf(angle_rad);
    }

    // Calculate average direction (radians)
    float avg_angle_rad = atan2f(sin_sum, cos_sum);

    // Convert back to degrees, ensure in 0-360 range
    float avg_angle_deg = avg_angle_rad * 180.0f / 3.14159f;
    if (avg_angle_deg < 0) {
        avg_angle_deg += 360.0f;
    }

    return avg_angle_deg;
}

// Handle new GPS data
void GPS_HandleNewData(char *nmea_data)
{
    if (nmea_data == NULL || strlen(nmea_data) < 10 || nmea_data[0] != '$') {
        return;
    }

    // Copy NMEA sentence to global buffer
    strcpy(gps_nmea_buffer, nmea_data);
    gps_new_data = 1;  // Set new data flag

    // Process GPS data
    GPS_Process(nmea_data);

    // Set data ready flag
    gps_data_ready = 1;
}

// Update GPS data (process course)
void GPS_UpdateData(void)
{
    if (gps_new_data && gps_data.valid) {
        // Process course data
        float speed_kmh = gps_data.speed * 1.852f;
        float processed_course = GPS_ProcessCourse(gps_data.course, speed_kmh);

        // Update course value in GPS data structure to processed value
        gps_data.course = processed_course;

        // Clear new data flag
        gps_new_data = 0;
    }
}

// Check if GPS data is ready
uint8_t GPS_IsDataReady(void)
{
    return gps_data_ready;
}

// Clear GPS data ready flag
void GPS_ClearDataReadyFlag(void)
{
    gps_data_ready = 0;
    gps_new_data = 0;
}

// Voltage monitoring test function - continuously monitor voltage changes throughout wake cycle
void GPS_VoltageMonitorTest(uint32_t duration_seconds)
{
    printf("=== Voltage Monitor Test Started ===\r\n");
    printf("Duration: %lu seconds\r\n", duration_seconds);
    printf("Monitoring voltage and temperature every 1 second...\r\n");

    uint32_t start_time = HAL_GetTick();
    uint32_t end_time = start_time + (duration_seconds * 1000);
    uint32_t last_print_time = start_time;
    uint32_t sample_count = 0;

    // Ensure ADC power is on
    extern ADC_HandleTypeDef hadc;
    extern uint32_t ADC_Value[60];  // 3通道×20采样=60个数据

    // Turn on ADC power (VCHK pin)
    ADC_ON;  // Use macro to turn on ADC power
    HAL_Delay(100); // Wait for power stabilization

    printf("ADC power enabled, starting voltage and temperature monitoring...\r\n");

    while (HAL_GetTick() < end_time) {
        uint32_t current_time = HAL_GetTick();

        // Read voltage every 1 second
        if ((current_time - last_print_time) >= 1000) {
            sample_count++;

            // Read ADC values - 3-channel DMA (Battery, Temperature, VREFINT)
            float voltage = 0.0f;
            float temperature = 0.0f;
            float adc_raw = 0.0f;
            float temp_raw = 0.0f;
            float vrefint_raw = 0.0f;

            if (HAL_ADC_Start_DMA(&hadc, (uint32_t*)&ADC_Value, 60) == HAL_OK) {
                HAL_Delay(100); // Wait for ADC sampling completion

                // Separate data from three channels
                uint32_t battery_adc_sum = 0;
                uint32_t temp_adc_sum = 0;
                uint32_t vrefint_adc_sum = 0;
                uint32_t battery_count = 0;
                uint32_t temp_count = 0;
                uint32_t vrefint_count = 0;

                // ADC configured with 3 channels, DMA data appears in sequence
                // Based on adc.c configuration order:
                // ADC_Value[0,3,6...] = Channel_6 (battery voltage)
                // ADC_Value[1,4,7...] = Channel_TEMPSENSOR (internal temperature)
                // ADC_Value[2,5,8...] = Channel_VREFINT (internal reference)
                for (int i = 0; i < 60; i += 3) {
                    if (i < 60) {
                        battery_adc_sum += ADC_Value[i];
                        battery_count++;
                    }
                    if (i + 1 < 60) {
                        temp_adc_sum += ADC_Value[i + 1];
                        temp_count++;
                    }
                    if (i + 2 < 60) {
                        vrefint_adc_sum += ADC_Value[i + 2];
                        vrefint_count++;
                    }
                }

                adc_raw = (battery_count > 0) ? (battery_adc_sum / (float)battery_count) : 0;
                temp_raw = (temp_count > 0) ? (temp_adc_sum / (float)temp_count) : 0;
                vrefint_raw = (vrefint_count > 0) ? (vrefint_adc_sum / (float)vrefint_count) : 0;

                // Convert to voltage value (consider VDD variation effect)
                // Use actual VDD instead of fixed 3.3V
                float actual_vdd = GPS_GetActualVDD(); // Get actual VDD voltage

                // Adjustable voltage divider ratio - adjust according to actual hardware
                float voltage_divider_ratio = 2.0f;  // Default 2:1 voltage divider ratio

                // Method 1: Use fixed VDD (original method)
                float voltage_fixed = (adc_raw * 3.3f / 4096.0f) * voltage_divider_ratio;

                // Method 2: Use actual VDD
                float voltage_vdd_corrected = (adc_raw * actual_vdd / 4096.0f) * voltage_divider_ratio;

                // Method 3: Use VREFINT data from DMA to correct VDD
                float actual_vdd_corrected = 3.3f;  // Default value

                if (vrefint_raw > 0) {
                    // Use VREFINT data from DMA to calculate actual VDD
                    #define VREFINT_CAL_ADDR    ((uint16_t*) 0x1FF80078)
                    #define VREFINT_CAL_VREF    3000  // mV

                    uint16_t vrefint_cal = *VREFINT_CAL_ADDR;
                    actual_vdd_corrected = (float)(VREFINT_CAL_VREF * vrefint_cal) / vrefint_raw / 1000.0f;
                }

                float voltage_vrefint_corrected = (adc_raw * actual_vdd_corrected / 4096.0f) * voltage_divider_ratio;

                // Select final result
                if (vrefint_raw > 0 && actual_vdd_corrected > 2.5f && actual_vdd_corrected < 3.6f) {
                    voltage = voltage_vrefint_corrected;  // Use VREFINT corrected result
                } else {
                    voltage = voltage_fixed;  // Use fixed VDD when VREFINT fails
                }

                // Apply calibration factor and offset to compensate for hardware errors
                voltage = voltage * VOLTAGE_CALIBRATION_FACTOR + VOLTAGE_OFFSET;

                // Calculate internal temperature using STM32L071 formula
                if (temp_raw > 0 && vrefint_raw > 0) {
                    // STM32L071 temperature sensor calibration values
                    #define TEMP30_CAL_ADDR   ((uint16_t*) 0x1FF8007A)  // 30°C calibration value
                    #define TEMP130_CAL_ADDR  ((uint16_t*) 0x1FF8007E)  // 130°C calibration value
                    #define TEMP_CAL_VREF     3000  // mV

                    uint16_t temp30_cal = *TEMP30_CAL_ADDR;
                    uint16_t temp130_cal = *TEMP130_CAL_ADDR;

                    // Calculate actual VDD for temperature correction
                    uint16_t vrefint_cal = *VREFINT_CAL_ADDR;
                    float actual_vdd_temp = (float)(VREFINT_CAL_VREF * vrefint_cal) / vrefint_raw / 1000.0f;

                    // Correct temperature reading for VDD variation
                    float temp_corrected = temp_raw * actual_vdd_temp / 3.0f;

                    // Calculate temperature using linear interpolation
                    temperature = 30.0f + (temp_corrected - temp30_cal) * (130.0f - 30.0f) / (temp130_cal - temp30_cal);
                }

                HAL_ADC_Stop_DMA(&hadc);
            } else {
                printf("ADC start failed at sample #%lu\r\n", sample_count);
                continue;
            }

            // Calculate elapsed time
            uint32_t elapsed_seconds = (current_time - start_time) / 1000;

            // Enhanced output: show voltage, temperature and raw ADC values
            printf("T+%02lu:%02lu - %.3fV, %.1f°C (ADC: %.0f, %.0f, %.0f)",
                   elapsed_seconds / 60, elapsed_seconds % 60,
                   voltage, temperature, adc_raw, temp_raw, vrefint_raw);

            printf("\r\n");

            last_print_time = current_time;
        }

        // Brief delay to avoid high CPU usage
        HAL_Delay(50);
    }

    // Turn off ADC power
    ADC_OFF;  // Use macro to turn off ADC power

    printf("=== Voltage Monitor Test Completed ===\r\n");
    printf("Total samples: %lu\r\n", sample_count);
}

// Simplified voltage reading function - single quick read
float GPS_ReadVoltageQuick(void)
{
    extern ADC_HandleTypeDef hadc;
    extern uint32_t ADC_Value[100];

    // Turn on ADC power
    ADC_ON;  // Use macro to turn on ADC power
    HAL_Delay(10); // Brief wait for power stabilization

    float voltage = 0.0f;

    if (HAL_ADC_Start_DMA(&hadc, (uint32_t*)&ADC_Value, 50) == HAL_OK) {
        HAL_Delay(50); // Wait for ADC sampling completion

        // Only calculate average of battery voltage channel (even indices)
        uint32_t battery_adc_sum = 0;
        uint32_t battery_count = 0;
        for (int i = 0; i < 50; i += 2) {
            if (i < 50) {
                battery_adc_sum += ADC_Value[i];
                battery_count++;
            }
        }
        float adc_avg = (battery_count > 0) ? (battery_adc_sum / (float)battery_count) : 0;

        // Convert to voltage value (assume 2:1 voltage divider ratio)
        voltage = (adc_avg * 3.3f / 4096.0f) * 2.0f;

        HAL_ADC_Stop_DMA(&hadc);

        printf("Quick voltage read: %.3fV (ADC=%.0f)\r\n", voltage, adc_avg);
    } else {
        printf("Quick voltage read failed\r\n");
    }

    // Turn off ADC power
    ADC_OFF;  // Use macro to turn off ADC power

    return voltage;
}

// Get actual VDD voltage value (through internal reference voltage correction)
float GPS_GetActualVDD(void)
{
    // STM32L071 internal reference voltage is 1.2V (typical value)
    extern ADC_HandleTypeDef hadc;

    // STM32L071 VREFINT calibration value address and reference voltage
    #define VREFINT_CAL_ADDR    ((uint16_t*) 0x1FF80078)  // Calibration value address
    #define VREFINT_CAL_VREF    3000             // Reference voltage during calibration

    static float cached_vdd = 3.3f;  // Cached VDD value
    uint32_t current_time = HAL_GetTick();

    // This function is deprecated, recommend using VREFINT data from DMA directly
    // Return default value to avoid interfering with DMA acquisition
    cached_vdd = 3.3f;

    printf("GPS_GetActualVDD() deprecated - use DMA VREFINT data instead\r\n");

    return cached_vdd;
}

// Improved voltage conversion function (considering VDD variation)
float GPS_ConvertVoltageWithVDDCorrection(float adc_raw)
{
    // Method 1: Use fixed 3.3V reference (current method, has error)
    float voltage_fixed = (adc_raw * 3.3f / 4096.0f) * 2.0f;

    // Method 2: Correction considering VDD variation
    float actual_vdd = GPS_GetActualVDD();
    float voltage_corrected = (adc_raw * actual_vdd / 4096.0f) * 2.0f;

    // Output comparison information for debugging
    printf("Voltage comparison: Fixed=%.3fV, Corrected=%.3fV (VDD=%.3fV)\r\n",
           voltage_fixed, voltage_corrected, actual_vdd);

    return voltage_corrected;
}

// Simplified battery voltage reading function (using VREFINT correction)
float GPS_ReadBatteryVoltage(void)
{
    extern ADC_HandleTypeDef hadc;
    extern uint32_t ADC_Value[100];

    // Turn on ADC power
    ADC_ON;  // Use macro to turn on ADC power
    HAL_Delay(10);

    float voltage = 0.0f;

    if (HAL_ADC_Start_DMA(&hadc, (uint32_t*)&ADC_Value, 100) == HAL_OK) {
        HAL_Delay(100);

        // Separate data from two channels
        uint32_t battery_adc_sum = 0;
        uint32_t vrefint_adc_sum = 0;
        uint32_t battery_count = 0;
        uint32_t vrefint_count = 0;

        for (int i = 0; i < 100; i += 2) {
            if (i < 100) {
                battery_adc_sum += ADC_Value[i];
                battery_count++;
            }
            if (i + 1 < 100) {
                vrefint_adc_sum += ADC_Value[i + 1];
                vrefint_count++;
            }
        }

        float adc_raw = (battery_count > 0) ? (battery_adc_sum / (float)battery_count) : 0;
        float vrefint_raw = (vrefint_count > 0) ? (vrefint_adc_sum / (float)vrefint_count) : 0;

        HAL_ADC_Stop_DMA(&hadc);

        // Calculate voltage using VREFINT correction
        if (vrefint_raw > 0) {
            #define VREFINT_CAL_ADDR_SIMPLE    ((uint16_t*) 0x1FF80078)
            #define VREFINT_CAL_VREF_SIMPLE    3000

            uint16_t vrefint_cal = *VREFINT_CAL_ADDR_SIMPLE;
            float actual_vdd = (float)(VREFINT_CAL_VREF_SIMPLE * vrefint_cal) / vrefint_raw / 1000.0f;

            // Calculate corrected voltage
            voltage = (adc_raw * actual_vdd / 4096.0f) * 2.0f;

            // Apply calibration factor and offset
            voltage = voltage * VOLTAGE_CALIBRATION_FACTOR + VOLTAGE_OFFSET;

        } else {
            // Use fixed VDD when VREFINT fails
            voltage = (adc_raw * 3.3f / 4096.0f) * 2.0f;
            voltage = voltage * VOLTAGE_CALIBRATION_FACTOR + VOLTAGE_OFFSET;
        }
    }

    // Turn off ADC power
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_4, GPIO_PIN_RESET);

    return voltage;
}

// VDD calibration function - calibrate VDD based on known actual battery voltage
float GPS_CalibrateVDD(float actual_battery_voltage, float adc_raw)
{
    // Reverse calculate VDD from actual battery voltage
    // Divided voltage = actual battery voltage / 2
    // ADC reading = divided voltage / VDD * 4096
    // So: VDD = divided voltage * 4096 / ADC reading

    float divided_voltage = actual_battery_voltage / 2.0f;
    float calculated_vdd = divided_voltage * 4096.0f / adc_raw;

    printf("VDD Calibration:\r\n");
    printf("  Actual battery: %.3fV\r\n", actual_battery_voltage);
    printf("  Divided voltage: %.3fV\r\n", divided_voltage);
    printf("  ADC reading: %.0f\r\n", adc_raw);
    printf("  Calculated VDD: %.3fV\r\n", calculated_vdd);

    return calculated_vdd;
}

// Voltage measurement diagnostic function
void GPS_VoltageDiagnostic(float known_battery_voltage)
{
    extern ADC_HandleTypeDef hadc;
    extern uint32_t ADC_Value[100];

    printf("\r\n=== Voltage Measurement Diagnostic ===\r\n");

    // Turn on ADC power
    ADC_ON;  // Use macro to turn on ADC power
    HAL_Delay(100);

    if (HAL_ADC_Start_DMA(&hadc, (uint32_t*)&ADC_Value, 100) == HAL_OK) {
        HAL_Delay(100);

        // Calculate average ADC value
        uint32_t adc_sum = 0;
        for (int i = 0; i < 100; i++) {
            adc_sum += ADC_Value[i];
        }
        float adc_avg = adc_sum / 100.0f;

        HAL_ADC_Stop_DMA(&hadc);

        // Calculate using fixed 3.3V
        float raw_voltage_33 = adc_avg * 3.3f / 4096.0f;
        float battery_voltage_33 = raw_voltage_33 * 2.0f;

        // Calibrate VDD
        float calibrated_vdd = GPS_CalibrateVDD(known_battery_voltage, adc_avg);

        // Calculate using calibrated VDD
        float raw_voltage_cal = adc_avg * calibrated_vdd / 4096.0f;
        float battery_voltage_cal = raw_voltage_cal * 2.0f;

        printf("Results:\r\n");
        printf("  ADC reading: %.0f/4096\r\n", adc_avg);
        printf("  Using VDD=3.3V: Raw=%.3fV, Battery=%.3fV\r\n",
               raw_voltage_33, battery_voltage_33);
        printf("  Using VDD=%.3fV: Raw=%.3fV, Battery=%.3fV\r\n",
               calibrated_vdd, raw_voltage_cal, battery_voltage_cal);
        printf("  Actual battery (measured): %.3fV\r\n", known_battery_voltage);
        printf("  Error with VDD=3.3V: %.3fV\r\n",
               battery_voltage_33 - known_battery_voltage);
        printf("  Error with calibrated VDD: %.3fV\r\n",
               battery_voltage_cal - known_battery_voltage);
    }

    // Turn off ADC power
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_4, GPIO_PIN_RESET);

    printf("=====================================\r\n\r\n");
}

// Read internal reference voltage VREFINT ADC value
uint16_t GPS_ReadVREFINT(void)
{
    extern ADC_HandleTypeDef hadc;
    uint16_t vrefint_value = 0;

    printf("Reading VREFINT (channel already configured in adc.c)...\r\n");

    // STM32L071 needs to enable internal reference voltage
    // Enable VREFINT
    HAL_ADCEx_EnableVREFINT();
    HAL_Delay(10); // Wait for VREFINT to stabilize

    // Stop current DMA conversion
    HAL_ADC_Stop_DMA(&hadc);

    // Configure VREFINT channel
    ADC_ChannelConfTypeDef sConfig = {0};
    sConfig.Channel = ADC_CHANNEL_VREFINT;
    sConfig.Rank = ADC_RANK_CHANNEL_NUMBER;
    // STM32L0 series ADC sampling time is set globally during ADC initialization, not in channel configuration

    if (HAL_ADC_ConfigChannel(&hadc, &sConfig) == HAL_OK) {
        printf("VREFINT channel configured\r\n");

        // Start single ADC conversion
        if (HAL_ADC_Start(&hadc) == HAL_OK) {
            printf("ADC started for VREFINT\r\n");

            // Wait for conversion completion
            if (HAL_ADC_PollForConversion(&hadc, 1000) == HAL_OK) {
                vrefint_value = HAL_ADC_GetValue(&hadc);
                printf("VREFINT read successful: %d\r\n", vrefint_value);
            } else {
                printf("VREFINT conversion timeout\r\n");
            }
            HAL_ADC_Stop(&hadc);
        } else {
            printf("Failed to start ADC for VREFINT\r\n");
        }
    } else {
        printf("Failed to configure VREFINT channel\r\n");
    }

    // Restore to battery voltage detection channel
    sConfig.Channel = ADC_CHANNEL_6;  // Restore to PA6 channel
    HAL_ADC_ConfigChannel(&hadc, &sConfig);

    // Disable VREFINT to save power
    HAL_ADCEx_DisableVREFINT();

    if (vrefint_value == 0) {
        printf("VREFINT read failed - value is 0\r\n");
    }

    return vrefint_value;
}

// Calculate actual VDD voltage using VREFINT (complete version)
float GPS_GetActualVDD_WithVREFINT(void)
{
    #define VREFINT_CAL_ADDR    ((uint16_t*) 0x1FF80078)  // Calibration value address
    #define VREFINT_CAL_VREF    3000                      // Reference voltage during calibration (mV)

    // Read calibration value
    uint16_t vrefint_cal = *VREFINT_CAL_ADDR;

    // Read current VREFINT ADC value
    uint16_t vrefint_data = GPS_ReadVREFINT();

    if (vrefint_data > 0 && vrefint_cal > 0) {
        // Calculate actual VDD
        float vdd = (float)(VREFINT_CAL_VREF * vrefint_cal) / vrefint_data / 1000.0f;

        printf("VREFINT calculation: CAL=%d, DATA=%d, VDD=%.3fV\r\n",
               vrefint_cal, vrefint_data, vdd);

        return vdd;
    } else {
        printf("VREFINT read failed: CAL=%d, DATA=%d\r\n", vrefint_cal, vrefint_data);
        return 3.3f;  // Return default value
    }
}
