/* USER CODE BEGIN Header */
/**
  * File Name          : freertos.c
  * Description        : Code for freertos applications
  */
/* USER CODE END Header */
//这个份文件是2通道AD转换的的备份文件，用来给三通道AD转换文件做参考
/* Includes ------------------------------------------------------------------*/
#include "FreeRTOS.h"
#include "task.h"
#include "main.h"
#include "cmsis_os.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include <stdio.h>
#include <string.h>
#include "gpio.h"
#include "rtc.h"
#include "adc.h"
#include "usart.h"
#include "dma.h"
#include "spi.h"
#include "i2c.h"
#include "stm32l0xx_hal_pwr.h"
#include "SPI_FLASH/bsp_spi_flash.h"
#include "lsm6ds3.h"
#include "GPS.h"
#include "GM20.h"
#include "rtc_sync.h"
#include "system_modules.h"

// External variables
extern uint8_t uart1_rx_buffer[];
extern char gps_buffer[];
extern uint16_t gps_buffer_index;
#define GPS_BUFFER_SIZE 256

extern char gm20_buffer[];
extern uint16_t gm20_buffer_index;
extern uint8_t gm20_rx_byte;
#define GM20_BUFFER_SIZE 256

extern GPS_Data_t gps_data;
extern uint8_t gps_new_data;
extern uint8_t gps_data_ready;
extern int ADC_Value[];
extern float pw;

// Function declarations
extern HAL_StatusTypeDef Create_Data_String(char *output_buffer, uint16_t buffer_size);
extern HAL_StatusTypeDef Send_Data_To_GM20(const char *data_string);
extern void GPS_ParseData(void);

// Modular function declarations
// Power management module
HAL_StatusTypeDef PowerModule_Init(void);
HAL_StatusTypeDef PowerModule_ReadBatteryVoltage(void);
void PowerModule_EnablePeripherals(void);
void PowerModule_DisablePeripherals(void);

// GPS module
HAL_StatusTypeDef GPSModule_Init(void);
HAL_StatusTypeDef GPSModule_WaitForData(uint32_t timeout_ms, uint8_t is_first_boot);
void GPSModule_PowerOn(void);
void GPSModule_PowerOff(void);
HAL_StatusTypeDef GPSModule_SyncRTC(void);

// Sensor module
HAL_StatusTypeDef SensorModule_Init(void);
HAL_StatusTypeDef SensorModule_ReadData(void);

// GM20 communication module
HAL_StatusTypeDef GM20Module_Init(uint8_t is_first_boot);
HAL_StatusTypeDef GM20Module_WakeUp(void);
HAL_StatusTypeDef GM20Module_SendData(void);
void GM20Module_SmartWait(void);
void GM20Module_PowerOn(void);
void GM20Module_PowerOff(void);

// Data processing module
HAL_StatusTypeDef DataModule_CreatePacket(char *output_buffer, uint16_t buffer_size);
HAL_StatusTypeDef DataModule_SendToGM20(const char *data_string);

// System control module
void SystemModule_PrintCurrentTime(void);
void SystemModule_EnterSleepMode(void);

// Battery protection module
uint8_t BatteryProtection_CheckVoltage(void);
void BatteryProtection_EnterLowVoltageMode(void);
void BatteryProtection_ExitLowVoltageMode(void);
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

// ADC power control through HAL library functions

// GM20 module configuration
#define GM20_WAIT_TIME_SECONDS 2       // GM20模块基础等待信号时间（秒）

// Sleep configuration
#define SLEEP_DURATION_SECONDS 30       // 休眠时间设置
#define BLINK_COUNT     3               // LED blink count
#define BLINK_DELAY     100             // LED blink delay in ms
#define UART1_RX_BUFFER_SIZE 256        // UART receive buffer size

// Battery voltage protection thresholds
#define BATTERY_LOW_VOLTAGE_THRESHOLD    3.4f    // 低电压保护阈值（V）
#define BATTERY_RECOVERY_VOLTAGE_THRESHOLD 3.6f  // 电压恢复阈值（V）

// Voltage monitoring test configuration
#define ENABLE_VOLTAGE_MONITOR_TEST 0   // 1=启用电压监测测试, 0=禁用
#define VOLTAGE_MONITOR_DURATION 160     // 电压监测持续时间（秒）


/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN Variables */
volatile uint8_t rtcWakeupFlag = 0;  // RTC wakeup flag
extern uint8_t uart1_rx_buffer[UART1_RX_BUFFER_SIZE]; // UART receive buffer

// LSM6DS3 sensor data structures - defined in main.c
extern LSM6DS3_Data imuData;        // IMU sensor data
extern LSM6DS3_Attitude attitude;   // Attitude data from sensor fusion - defined in main.c

// Dynamic sleep time control variables
uint32_t wakeup_counter = 0;        // Dynamic wakeup counter, 0 means use default value

// Global first boot flag
uint8_t is_first_boot = 1;          // First boot flag for forced RTC sync

// Battery voltage protection status flag
uint8_t low_voltage_protection_active = 0;  // Low voltage protection status: 0=normal mode, 1=protection mode

// RTOS communication objects
// Queue handles
osMessageQId gpsDataQueueHandle;
osMessageQId sensorDataQueueHandle;
osMessageQId gm20CmdQueueHandle;

// Semaphore handles
osSemaphoreId gpsReadySemHandle;
osSemaphoreId sensorReadySemHandle;
osSemaphoreId dataSentSemHandle;

// Event group handles - using semaphores to simulate event group functionality
osSemaphoreId gpsStartSemHandle;
osSemaphoreId sensorStartSemHandle;

// Event group flag definitions
#define GPS_READY_BIT       (1UL << 0)
#define SENSOR_READY_BIT    (1UL << 1)
#define DATA_SENT_BIT       (1UL << 2)
#define SLEEP_READY_BIT     (1UL << 3)

// Data structure definitions are in system_modules.h
/* USER CODE END Variables */
osThreadId GPSTaskHandle;
uint32_t GPSTaskBuffer[ 512 ];
osStaticThreadDef_t GPSTaskControlBlock;
osThreadId AccelTaskHandle;
uint32_t AccelTaskBuffer[ 512 ];
osStaticThreadDef_t AccelTaskControlBlock;
osThreadId GM20TaskHandle;
uint32_t GM20TaskBuffer[ 128 ];
osStaticThreadDef_t GM20TaskControlBlock;
osThreadId FlashTaskHandle;
uint32_t FlashTaskBuffer[ 128 ];
osStaticThreadDef_t FlashTaskControlBlock;
osThreadId myPowerTaskHandle;
uint32_t myPowerTaskBuffer[ 512 ];
osStaticThreadDef_t myPowerTaskControlBlock;

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN FunctionPrototypes */
void EnterStopMode(void);
void BlinkLEDs(uint8_t count, uint32_t delay);
void SystemClock_Config(void);  // System clock config function defined in main.c
/* USER CODE END FunctionPrototypes */

void StartGPSTask(void const * argument);
void StartAccelTask(void const * argument);
void StartGM20Task(void const * argument);
void StartFlashTask(void const * argument);
void StartPowerTask(void const * argument);

void MX_FREERTOS_Init(void); /* (MISRA C 2004 rule 8.1) */

/* GetIdleTaskMemory prototype (linked to static allocation support) */
void vApplicationGetIdleTaskMemory( StaticTask_t **ppxIdleTaskTCBBuffer, StackType_t **ppxIdleTaskStackBuffer, uint32_t *pulIdleTaskStackSize );

/* USER CODE BEGIN GET_IDLE_TASK_MEMORY */
static StaticTask_t xIdleTaskTCBBuffer;
static StackType_t xIdleStack[configMINIMAL_STACK_SIZE];

void vApplicationGetIdleTaskMemory( StaticTask_t **ppxIdleTaskTCBBuffer, StackType_t **ppxIdleTaskStackBuffer, uint32_t *pulIdleTaskStackSize )
{
  *ppxIdleTaskTCBBuffer = &xIdleTaskTCBBuffer;
  *ppxIdleTaskStackBuffer = &xIdleStack[0];
  *pulIdleTaskStackSize = configMINIMAL_STACK_SIZE;
  /* place for user code */
}
/* USER CODE END GET_IDLE_TASK_MEMORY */

/**
  * @brief  FreeRTOS initialization
  * @param  None
  * @retval None
  */
void MX_FREERTOS_Init(void) {
  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* USER CODE BEGIN RTOS_MUTEX */
  /* add mutexes, ... */
  /* USER CODE END RTOS_MUTEX */

  /* USER CODE BEGIN RTOS_SEMAPHORES */
  // Create data ready semaphores
  osSemaphoreDef(gpsReadySem);
  gpsReadySemHandle = osSemaphoreCreate(osSemaphore(gpsReadySem), 1);

  osSemaphoreDef(sensorReadySem);
  sensorReadySemHandle = osSemaphoreCreate(osSemaphore(sensorReadySem), 1);

  osSemaphoreDef(dataSentSem);
  dataSentSemHandle = osSemaphoreCreate(osSemaphore(dataSentSem), 1);

  // Create task start semaphores
  osSemaphoreDef(gpsStartSem);
  gpsStartSemHandle = osSemaphoreCreate(osSemaphore(gpsStartSem), 1);

  osSemaphoreDef(sensorStartSem);
  sensorStartSemHandle = osSemaphoreCreate(osSemaphore(sensorStartSem), 1);

  // Initial state: acquire data ready semaphores, wait for task release
  osSemaphoreWait(gpsReadySemHandle, 0);
  osSemaphoreWait(sensorReadySemHandle, 0);
  osSemaphoreWait(dataSentSemHandle, 0);

  // Initial state: acquire start semaphores, wait for main task release
  osSemaphoreWait(gpsStartSemHandle, 0);
  osSemaphoreWait(sensorStartSemHandle, 0);
  /* USER CODE END RTOS_SEMAPHORES */

  /* USER CODE BEGIN RTOS_TIMERS */
  /* start timers, add new ones, ... */
  /* USER CODE END RTOS_TIMERS */

  /* USER CODE BEGIN RTOS_QUEUES */
  // Create message queues
  osMessageQDef(gpsDataQueue, 2, GPSQueueData_t);
  gpsDataQueueHandle = osMessageCreate(osMessageQ(gpsDataQueue), NULL);

  osMessageQDef(sensorDataQueue, 2, SensorQueueData_t);
  sensorDataQueueHandle = osMessageCreate(osMessageQ(sensorDataQueue), NULL);

  osMessageQDef(gm20CmdQueue, 4, GM20CmdData_t);
  gm20CmdQueueHandle = osMessageCreate(osMessageQ(gm20CmdQueue), NULL);
  /* USER CODE END RTOS_QUEUES */

  /* Create the thread(s) */
  /* definition and creation of GPSTask */
  osThreadStaticDef(GPSTask, StartGPSTask, osPriorityNormal, 0, 512, GPSTaskBuffer, &GPSTaskControlBlock);
  GPSTaskHandle = osThreadCreate(osThread(GPSTask), NULL);

  /* definition and creation of AccelTask */
  osThreadStaticDef(AccelTask, StartAccelTask, osPriorityNormal, 0, 512, AccelTaskBuffer, &AccelTaskControlBlock);
  AccelTaskHandle = osThreadCreate(osThread(AccelTask), NULL);

  /* definition and creation of GM20Task */
  osThreadStaticDef(GM20Task, StartGM20Task, osPriorityIdle, 0, 128, GM20TaskBuffer, &GM20TaskControlBlock);
  GM20TaskHandle = osThreadCreate(osThread(GM20Task), NULL);

  /* definition and creation of FlashTask */
  osThreadStaticDef(FlashTask, StartFlashTask, osPriorityIdle, 0, 128, FlashTaskBuffer, &FlashTaskControlBlock);
  FlashTaskHandle = osThreadCreate(osThread(FlashTask), NULL);

  /* definition and creation of myPowerTask - Main Control Task */
  osThreadStaticDef(myPowerTask, StartPowerTask, osPriorityHigh, 0, 512, myPowerTaskBuffer, &myPowerTaskControlBlock);
  myPowerTaskHandle = osThreadCreate(osThread(myPowerTask), NULL);

  /* USER CODE BEGIN RTOS_THREADS */
  printf("RTOS communication objects created successfully\r\n");
  /* USER CODE END RTOS_THREADS */

}

/* USER CODE BEGIN Header_StartGPSTask */
// GPS task implementation
/* USER CODE END Header_StartGPSTask */
void StartGPSTask(void const * argument)
{
  /* USER CODE BEGIN StartGPSTask */



  /* Infinite loop */
  for(;;)
  {
    // Wait for main task start signal
    if (osSemaphoreWait(gpsStartSemHandle, osWaitForever) == osOK) {
      // Execute GPS data acquisition
      GPSModule_PowerOn();
      HAL_StatusTypeDef gps_result = GPSModule_WaitForData(60000, is_first_boot);

      // GPS clock sync logic optimization (before turning off GPS power)
      if (gps_result == HAL_OK) {
        // Wait for GPS data to stabilize completely
        HAL_Delay(1000);

        if (is_first_boot) {
          // First boot forced RTC sync
          if (GPSModule_SyncRTC() == HAL_OK) {
            is_first_boot = 0;
          }
        } else {
          // Normal working cycle conditional sync
          GPSModule_SyncRTC();
        }
      }

      // Turn off GPS power after RTC sync to save power
      GPSModule_PowerOff();

      // Release GPS data ready semaphore
      osSemaphoreRelease(gpsReadySemHandle);
    }

    // Brief delay to avoid excessive CPU usage
    osDelay(10);
  }
  /* USER CODE END StartGPSTask */
}

/* USER CODE BEGIN Header_StartAccelTask */
// Accelerometer/sensor task implementation
/* USER CODE END Header_StartAccelTask */
void StartAccelTask(void const * argument)
{
  /* USER CODE BEGIN StartAccelTask */



  /* Infinite loop */
  for(;;)
  {
    // Wait for main task start signal
    if (osSemaphoreWait(sensorStartSemHandle, osWaitForever) == osOK) {
      // Execute sensor data reading
      SensorModule_Init();
      SensorModule_ReadData();

      // Release sensor data ready semaphore
      osSemaphoreRelease(sensorReadySemHandle);
    }

    // Brief delay to avoid excessive CPU usage
    osDelay(10);
  }
  /* USER CODE END StartAccelTask */
}

/* USER CODE BEGIN Header_StartGM20Task */
// GM20 task implementation
/* USER CODE END Header_StartGM20Task */
void StartGM20Task(void const * argument)
{
  /* USER CODE BEGIN StartGM20Task */
  // GM20 task is now managed by StartPowerTask, this task remains idle
  for(;;)
  {
    osDelay(10);
  }
  /* USER CODE END StartGM20Task */
}

/* USER CODE BEGIN Header_StartFlashTask */
// Flash task implementation
/* USER CODE END Header_StartFlashTask */
void StartFlashTask(void const * argument)
{
  /* USER CODE BEGIN StartFlashTask */
  /* Infinite loop */
  for(;;)
  {
    osDelay(1);
  }
  /* USER CODE END StartFlashTask */
}

/* USER CODE BEGIN Header_StartPowerTask */
// Main power control task implementation
/* USER CODE END Header_StartPowerTask */
void StartPowerTask(void const * argument)
{
  /* USER CODE BEGIN StartPowerTask */
  uint32_t cycle_count = 0;

//  printf("Main Control Task started\r\n");

  // Initialize GM20 module on first startup
  GM20Module_Init(is_first_boot);

  /* Infinite loop */
  for(;;)
  {
    cycle_count++;
    printf("===== Cycle #%lu =====\r\n", cycle_count);

    // 1. Print RTC time immediately after wakeup
    SystemModule_PrintCurrentTime();

    // 2. Power management module initialization
    PowerModule_Init();

    // 3. Check battery voltage
    PowerModule_ReadBatteryVoltage();

    // 4. Battery voltage protection check
    uint8_t voltage_protection_result = BatteryProtection_CheckVoltage();
    if (voltage_protection_result == 1) {
        // Enter low voltage protection mode, skip all data collection work
        printf("Low voltage protection active - skipping data collection\r\n");
        SystemModule_EnterSleepMode();
        continue; // Skip the rest of this loop
    } else if (voltage_protection_result == 2) {
        // Voltage recovered, exit low voltage protection mode
        printf("Voltage recovered - resuming normal operation\r\n");
    }

#if ENABLE_VOLTAGE_MONITOR_TEST
    // 4.5. Voltage monitoring test - monitor voltage changes throughout the wake cycle
    printf("Starting voltage monitoring test...\r\n");
    GPS_VoltageMonitorTest(VOLTAGE_MONITOR_DURATION); // Monitor voltage changes
#endif

    // 5. Start GPS and sensor tasks (parallel execution)
    osSemaphoreRelease(gpsStartSemHandle);
    osSemaphoreRelease(sensorStartSemHandle);

    // 6. Wait for GPS and sensor data ready
    uint8_t gps_ok = 0, sensor_ok = 0;
    if (osSemaphoreWait(gpsReadySemHandle, 90000) == osOK) {
      gps_ok = 1;
    }
    if (osSemaphoreWait(sensorReadySemHandle, 5000) == osOK) {
      sensor_ok = 1;
    }

    // Print data collection results
    if (gps_ok && gps_data.valid) {
//      printf("GPS: %d sats, HDOP=%.1f\r\n", gps_data.satellites, gps_data.hdop);
    } else {
      printf("GPS: No signal\r\n");
    }

    if (sensor_ok) {
      printf("Sensor: %.1fC, Roll=%.1f, Pitch=%.1f\r\n",
             imuData.temp_celsius, attitude.roll, attitude.pitch);
    }

    // 7. GM20 module communication and data transmission
    if (GM20Module_WakeUp() == HAL_OK) {
        if (GM20Module_SendData() == HAL_OK) {
          // Smart wait: monitor signal strength, continue working if signal present until signal disappears
//          GM20Module_SmartWait();   // GM20 always powered, no need for signal detection mechanism
        }
    }

    // 8. Enter sleep mode
    SystemModule_EnterSleepMode();

    printf("Cycle #%lu completed\r\n\n", cycle_count);

    // Note: is_first_boot flag is cleared by GPS task after successful RTC sync
    osDelay(500);
  }
  /* USER CODE END StartPowerTask */
}

/* Private application code --------------------------------------------------*/
/* USER CODE BEGIN Application */

// Blink LEDs alternately
void BlinkLEDs(uint8_t count, uint32_t delay)
{
  for (uint8_t i = 0; i < count; i++)
  {
    LED1_ON;
    LED2_OFF;
    osDelay(delay);

    LED1_OFF;
    LED2_ON;
    osDelay(delay);
  }

  // Turn off all LEDs at the end
  LED1_OFF;
  LED2_OFF;
}

// Enter STOP mode - ultra simplified version
void EnterStopMode(void)
{
  // Turn off LEDs
  LED1_OFF;
  LED2_OFF;

  // Turn off all peripheral power to save energy
  GPS_PWR_OFF;  // Turn off GPS power using correct macro
  V_OUT_OFF;    // Turn off sensor power using macro
  ADC_OFF;      // Turn off ADC power using macro

  // Turn off GM20 module power to save power
//  RF_PWR_OFF;

  // Stop UART receive interrupts
  HAL_UART_AbortReceive_IT(&huart1);
  HAL_UART_AbortReceive_IT(&hlpuart1);

  // Disable UART interrupts
  HAL_NVIC_DisableIRQ(USART1_IRQn);
  HAL_NVIC_DisableIRQ(LPUART1_IRQn);

  // Turn off all peripheral clocks
  __HAL_RCC_I2C1_CLK_DISABLE();
  __HAL_RCC_USART1_CLK_DISABLE();
  __HAL_RCC_LPUART1_CLK_DISABLE();

  // Ensure UART transmission is complete
  while(__HAL_UART_GET_FLAG(&huart1, UART_FLAG_TC) == RESET);
  HAL_Delay(10);

  // Configure RTC wakeup timer

  // Ensure all interrupts are processed
  __disable_irq();

  // Deactivate any existing wakeup timer
  HAL_RTCEx_DeactivateWakeUpTimer(&hrtc);

  // Clear any pending flags
  __HAL_RTC_WAKEUPTIMER_CLEAR_FLAG(&hrtc, RTC_FLAG_WUTF);
  __HAL_RTC_WAKEUPTIMER_EXTI_CLEAR_FLAG();

  // Set wakeup timer with RTC_WAKEUPCLOCK_CK_SPRE_16BITS (1Hz clock source)
  // Use external variable or default value
  // Sleep time = Counter / 1Hz, so Counter = sleep seconds - 1
  uint32_t default_wakeup_counter = SLEEP_DURATION_SECONDS - 1;
  uint32_t actual_wakeup_counter = (wakeup_counter > 0) ? wakeup_counter : default_wakeup_counter;

  // Setting RTC wakeup counter

  if (HAL_RTCEx_SetWakeUpTimer_IT(&hrtc, actual_wakeup_counter, RTC_WAKEUPCLOCK_CK_SPRE_16BITS) != HAL_OK) {
    printf("Failed to set wakeup timer\r\n");
    __enable_irq();
    return;
  }

  // Enable RTC interrupt with highest priority
  HAL_NVIC_SetPriority(RTC_IRQn, 0, 0);
  HAL_NVIC_EnableIRQ(RTC_IRQn);

  // Enable EXTI for RTC wakeup
  __HAL_RTC_WAKEUPTIMER_EXTI_ENABLE_IT();
  __HAL_RTC_WAKEUPTIMER_EXTI_ENABLE_RISING_EDGE();

  __enable_irq();

  // Get current time
  RTC_TimeTypeDef time_before;
  RTC_DateTypeDef date_before;
  HAL_RTC_GetTime(&hrtc, &time_before, RTC_FORMAT_BIN);
  HAL_RTC_GetDate(&hrtc, &date_before, RTC_FORMAT_BIN);

  printf("Time before sleep: %02d:%02d:%02d\r\n",
         time_before.Hours, time_before.Minutes, time_before.Seconds);

  // Make sure the message is sent
  while(__HAL_UART_GET_FLAG(&huart1, UART_FLAG_TC) == RESET);
  HAL_Delay(10);

  // Disable all interrupts, prepare to enter low power mode
  __disable_irq();

  // Disable SysTick interrupt
  SysTick->CTRL &= ~SysTick_CTRL_TICKINT_Msk;

  // Disable all interrupts, keep only RTC wakeup interrupt
  for (uint8_t i = 0; i < 8; i++) {
    NVIC->ICER[i] = 0xFFFFFFFF;
  }

  // Ensure RTC wakeup interrupt is enabled
  HAL_NVIC_SetPriority(RTC_IRQn, 0, 0);
  HAL_NVIC_EnableIRQ(RTC_IRQn);

  // Clear all pending interrupts
  for (uint8_t i = 0; i < 8; i++) {
    NVIC->ICPR[i] = 0xFFFFFFFF;
  }

  // Suspend scheduler
  vTaskSuspendAll();

  // Re-enable global interrupts, but only RTC interrupt is active
  __enable_irq();

  // Enter STOP mode with low power regulator
  HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);

  // After wakeup - code below executes after waking up

  // Turn on LED to indicate wakeup started
  LED1_ON;

  // Reconfigure system clock - required after STOP mode
  SystemClock_Config();

  // Re-initialize all necessary peripheral clocks
  __HAL_RCC_USART1_CLK_ENABLE();
  __HAL_RCC_LPUART1_CLK_ENABLE();
  __HAL_RCC_I2C1_CLK_ENABLE();
  __HAL_RCC_DMA1_CLK_ENABLE();

  // Re-initialize UART1 and LPUART1
  MX_USART1_UART_Init();
  MX_LPUART1_UART_Init();

  // Re-enable UART interrupts
  HAL_NVIC_SetPriority(USART1_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(USART1_IRQn);
  HAL_NVIC_SetPriority(LPUART1_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(LPUART1_IRQn);

  // Restart UART receive interrupts
  HAL_UART_Receive_IT(&huart1, &uart1_rx_buffer[0], 1);

  // Re-enable SysTick interrupt
  SysTick->CTRL |= SysTick_CTRL_TICKINT_Msk;

  // Re-enable global interrupts
  __enable_irq();

  // Blink LED to indicate wakeup in progress
  LED1_OFF;
  HAL_Delay(100);
  LED1_ON;

  // Get current time
  RTC_TimeTypeDef time_after;
  RTC_DateTypeDef date_after;
  HAL_RTC_GetTime(&hrtc, &time_after, RTC_FORMAT_BIN);
  HAL_RTC_GetDate(&hrtc, &date_after, RTC_FORMAT_BIN);

  // Resume scheduler
  xTaskResumeAll();

  // Stop wakeup timer
  HAL_RTCEx_DeactivateWakeUpTimer(&hrtc);

//  printf("Woke up from sleep mode\r\n");
  printf("Time after sleep: %02d:%02d:%02d\r\n",
         time_after.Hours, time_after.Minutes, time_after.Seconds);

  // Calculate elapsed time
  int elapsed_seconds = (time_after.Hours - time_before.Hours) * 3600 +
                        (time_after.Minutes - time_before.Minutes) * 60 +
                        (time_after.Seconds - time_before.Seconds);
  if (elapsed_seconds < 0) {
    elapsed_seconds += 24 * 3600; // Handle day rollover
  }

  printf("Elapsed time: %d seconds\r\n", elapsed_seconds);

  // Re-enable peripherals
  PowerModule_EnablePeripherals();
}

// RTC wakeup timer event callback
void HAL_RTCEx_WakeUpTimerEventCallback(RTC_HandleTypeDef *hrtc)
{
  // Set wakeup flag
  rtcWakeupFlag = 1;

  // Clear RTC wakeup flag
  __HAL_RTC_WAKEUPTIMER_CLEAR_FLAG(hrtc, RTC_FLAG_WUTF);

  // Clear EXTI line flag
  __HAL_RTC_WAKEUPTIMER_EXTI_CLEAR_FLAG();

  // Note: Do not use printf here as it may crash the system when called from interrupt context
}



// Modular function implementations

// Power management module initialization
HAL_StatusTypeDef PowerModule_Init(void)
{
    // Turn on power switches
    ADC_ON;   // Turn on ADC voltage measurement switch
    V_OUT_ON; // Turn on 3-axis and SPI_FLASH power
    HAL_Delay(2000); // Wait for stabilization

    return HAL_OK;
}

// Read battery voltage using pure VREFINT calibration (consistent with voltage test)
HAL_StatusTypeDef PowerModule_ReadBatteryVoltage(void)
{
    extern ADC_HandleTypeDef hadc;
    extern int ADC_Value[100];

    // Turn on ADC power
    ADC_ON;  // Use macro to turn on ADC power
    HAL_Delay(10);

    float voltage = 0.0f;

    if (HAL_ADC_Start_DMA(&hadc, (uint32_t*)ADC_Value, 100) == HAL_OK) {
        HAL_Delay(100);

        // Separate data from two channels
        uint32_t battery_adc_sum = 0;
        uint32_t vrefint_adc_sum = 0;
        uint32_t battery_count = 0;
        uint32_t vrefint_count = 0;

        for (int i = 0; i < 100; i += 2) {
            if (i < 100) {
                battery_adc_sum += ADC_Value[i];
                battery_count++;
            }
            if (i + 1 < 100) {
                vrefint_adc_sum += ADC_Value[i + 1];
                vrefint_count++;
            }
        }

        float adc_raw = (battery_count > 0) ? (battery_adc_sum / (float)battery_count) : 0;
        float vrefint_raw = (vrefint_count > 0) ? (vrefint_adc_sum / (float)vrefint_count) : 0;

        HAL_ADC_Stop_DMA(&hadc);

        // Calculate voltage using VREFINT calibration and apply calibration coefficients
        if (vrefint_raw > 0) {
            #define VREFINT_CAL_ADDR_POWER    ((uint16_t*) 0x1FF80078)
            #define VREFINT_CAL_VREF_POWER    3000

            uint16_t vrefint_cal = *VREFINT_CAL_ADDR_POWER;
            float actual_vdd = (float)(VREFINT_CAL_VREF_POWER * vrefint_cal) / vrefint_raw / 1000.0f;

            // Calculate base voltage
            voltage = (adc_raw * actual_vdd / 4096.0f) * 2.0f;

        } else {
            // Use fixed VDD when VREFINT fails
            voltage = (adc_raw * 3.3f / 4096.0f) * 2.0f;
        }

        // Apply calibration factor and offset to compensate for hardware errors
        voltage = voltage * VOLTAGE_CALIBRATION_FACTOR + VOLTAGE_OFFSET;
    }

    // Turn off ADC power
    ADC_OFF;  // Use macro to turn off ADC power

    pw = voltage;
    printf("Battery: %.3f V\r\n", pw);

    return HAL_OK;
}

// Enable peripheral power
void PowerModule_EnablePeripherals(void)
{
    // Turn on power for GPS and sensors
    GPS_PWR_ON;
    V_OUT_ON;

    // Decide whether to turn on GM20 power based on low voltage protection status
    if (!low_voltage_protection_active) {
        RF_PWR_ON;  // Only turn on GM20 power in non-low voltage protection mode

        // Wait for GM20 power to stabilize
        HAL_Delay(300);  // Reduced delay: 500ms -> 300ms

        // Re-initialize LPUART1 (because GM20 power was turned off)
        __HAL_RCC_LPUART1_CLK_ENABLE();
        MX_LPUART1_UART_Init();
        HAL_Delay(50);  // Reduced delay: 100ms -> 50ms

        // Re-enable LPUART1 receive interrupt
        HAL_UART_Receive_IT(&hlpuart1, &gm20_rx_byte, 1);
    } else {
        // In low voltage protection mode, ensure GM20 power is off
        RF_PWR_OFF;
        printf("GM20 power kept OFF due to low voltage protection\r\n");
    }

    // Clear GPS buffer
    memset(gps_buffer, 0, GPS_BUFFER_SIZE);
    gps_buffer_index = 0;

    // Ensure UART receive interrupt is started
    if (huart1.RxState != HAL_UART_STATE_BUSY_RX) {
        // If UART receive is not started, restart it
        HAL_UART_Receive_IT(&huart1, &uart1_rx_buffer[0], 1);
    }

    // Blink LED to indicate successful wakeup
    LED1_ON;
    HAL_Delay(50);  // Reduced delay: 100ms -> 50ms
    LED1_OFF;

    // Ensure I2C is initialized
    if (hi2c1.State == HAL_I2C_STATE_RESET) {
        MX_I2C1_Init();
    }
}

// Disable peripheral power
void PowerModule_DisablePeripherals(void)
{
    ADC_OFF;
    V_OUT_OFF;
    GPS_PWR_OFF;

    // In low voltage protection mode, ensure GM20 power is off
    if (low_voltage_protection_active) {
        RF_PWR_OFF; // Turn off GM20 module power to save power
    }
}

// GPS module initialization
HAL_StatusTypeDef GPSModule_Init(void)
{
    return HAL_OK;
}

// GPS module power on
void GPSModule_PowerOn(void)
{
    GPS_PWR_ON;
    HAL_Delay(200);
}

// GPS module power off
void GPSModule_PowerOff(void)
{
    GPS_PWR_OFF;
}

// Wait for GPS data
HAL_StatusTypeDef GPSModule_WaitForData(uint32_t timeout_ms, uint8_t is_first_boot)
{
    uint32_t gps_wait_start;
    uint8_t gps_data_valid = 0;

    // Reset GPS data
    gps_data.valid = 0;
    gps_data_ready = 0;
    gps_new_data = 0;
    gps_buffer_index = 0;
    memset(gps_buffer, 0, GPS_BUFFER_SIZE);

    // Set GPS wait timeout
    uint32_t gps_wait_timeout = is_first_boot ? 90000 : 60000; // 90s first boot, 60s normal

    gps_wait_start = HAL_GetTick();

    // Wait for GPS first fix
    while ((HAL_GetTick() - gps_wait_start) < gps_wait_timeout) {
        // Check if there's new GPS data to parse
        if (gps_new_data) {
            gps_new_data = 0;
            GPS_ParseData();
        }

        // Check if GPS data is valid
        if (gps_data.valid) {
            gps_data_valid = 1;
            break;
        }
        // LED blink to indicate waiting for GPS
        LED1_ON;
        osDelay(100);
        LED1_OFF;
        osDelay(100);
    }

    // GPS data acquisition complete
    if (gps_data_valid) {
        // GPS data ready, LED2 blinks 3 times to indicate successful GPS signal reception
        for (int i = 0; i < 3; i++) {
            LED2_ON;
            HAL_Delay(200);
            LED2_OFF;
            HAL_Delay(200);
        }
        return HAL_OK;
    } else {
        // Use all-zero default GPS data
        gps_data.latitude = 0.0;
        gps_data.longitude = 0.0;
        gps_data.altitude = 0.0;
        gps_data.fix_quality = 0;
        gps_data.speed = 0.0;
        gps_data.course = 0.0;
        gps_data.hdop = 99.9;
        gps_data.pdop = 99.9;
        return HAL_TIMEOUT;
    }
}

// GPS clock synchronization
HAL_StatusTypeDef GPSModule_SyncRTC(void)
{
    // Check if GPS data is valid
    if (!gps_data.valid) {
        return HAL_ERROR;
    }

    // Get current RTC time
    RTC_TimeTypeDef rtc_time;
    RTC_DateTypeDef rtc_date;
    HAL_RTC_GetTime(&hrtc, &rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &rtc_date, RTC_FORMAT_BIN);

    // Smart GPS time sync logic: when GPS has positioning info, time is definitely valid
    // Check if GPS time data is valid (time is more important than date)
    uint8_t time_valid = (gps_data.hour <= 23 && gps_data.minute <= 59 && gps_data.second <= 59);
    uint8_t date_valid = (gps_data.year >= 2020 && gps_data.month >= 1 &&
                         gps_data.month <= 12 && gps_data.day >= 1 && gps_data.day <= 31);

    // When GPS has positioning info, time is definitely valid, should not reject sync due to time format issues
    // Only skip sync when GPS is completely invalid
    if (!gps_data.valid) {
        return HAL_ERROR;
    }

    // If GPS has positioning but time format is abnormal, use default time handling
    if (!time_valid) {
        printf("GPS time format invalid, using RTC time for sync\r\n");
        return HAL_OK;  // Keep current RTC time
    }

    // Calculate time difference (minutes)
    int time_diff = abs((rtc_time.Hours - gps_data.hour) * 60 + (rtc_time.Minutes - gps_data.minute));

    // Sync RTC clock every time valid GPS data is obtained
    extern uint8_t is_first_boot;

    printf("GPS data valid, syncing RTC with GPS time\r\n");

    if (date_valid) {
        // GPS date valid, perform complete sync (GPS time + GPS date)
        if (GPS_SyncRTC(&gps_data)) {
            if (is_first_boot) {
                is_first_boot = 0; // Clear flag after successful first boot sync
                printf("First boot: RTC synchronized with GPS time and date: %02d:%02d:%02d %04d-%02d-%02d\r\n",
                       gps_data.hour, gps_data.minute, gps_data.second,
                       gps_data.year, gps_data.month, gps_data.day);
            } else {
                printf("RTC synchronized with GPS time and date: %02d:%02d:%02d %04d-%02d-%02d\r\n",
                       gps_data.hour, gps_data.minute, gps_data.second,
                       gps_data.year, gps_data.month, gps_data.day);
            }
            return HAL_OK;
        } else {
            printf("GPS full sync failed\r\n");
            return HAL_ERROR;
        }
    } else {
        // GPS date invalid but has positioning, use GPS time + keep current RTC date
        if (RTC_SetDateTime(gps_data.hour, gps_data.minute, gps_data.second,
                           rtc_date.Date, rtc_date.Month, rtc_date.Year) == HAL_OK) {
            if (is_first_boot) {
                is_first_boot = 0; // Clear flag after successful first boot sync
                printf("First boot: RTC synchronized with GPS time: %02d:%02d:%02d %02d/%02d/%02d\r\n",
                       gps_data.hour, gps_data.minute, gps_data.second,
                       rtc_date.Date, rtc_date.Month, rtc_date.Year);
            } else {
                printf("RTC synchronized with GPS time: %02d:%02d:%02d %02d/%02d/%02d\r\n",
                       gps_data.hour, gps_data.minute, gps_data.second,
                       rtc_date.Date, rtc_date.Month, rtc_date.Year);
            }
            return HAL_OK;
        } else {
            printf("GPS time sync failed\r\n");
            return HAL_ERROR;
        }
    }
}

// Sensor module initialization
HAL_StatusTypeDef SensorModule_Init(void)
{
//    printf("Sensor module initialization\r\n");

    // Initialize I2C
    __HAL_RCC_I2C1_CLK_ENABLE();
    MX_I2C1_Init();
    HAL_Delay(50);  // Reduced delay: 100ms -> 50ms

    return HAL_OK;
}

// Read sensor data
HAL_StatusTypeDef SensorModule_ReadData(void)
{
    if (HAL_I2C_IsDeviceReady(&hi2c1, LSM6DS3_ADDR, 3, 100) == HAL_OK) {
        LSM6DS3_Init(&hi2c1);
        LSM6DS3_InitAttitude(&attitude);
        HAL_Delay(20);  // Reduced delay: 50ms -> 20ms
        LSM6DS3_ReadData(&hi2c1, &imuData);
        LSM6DS3_ComplementaryFilter(&imuData, &attitude);
        return HAL_OK;
    } else {
        memset(&imuData, 0, sizeof(LSM6DS3_Data));
        imuData.temp_celsius = 25.0f;
        memset(&attitude, 0, sizeof(LSM6DS3_Attitude));
        return HAL_ERROR;
    }
}

// GM20 module initialization
HAL_StatusTypeDef GM20Module_Init(uint8_t is_first_boot)
{
    if (is_first_boot) {
//        printf("Initializing GM20 module...\r\n");
        RF_PWR_ON;
        HAL_Delay(800);  // Reduced delay: 1000ms -> 800ms

        __HAL_RCC_LPUART1_CLK_ENABLE();
        MX_LPUART1_UART_Init();
        HAL_Delay(50);  // Reduced delay: 100ms -> 50ms

        HAL_UART_Receive_IT(&hlpuart1, &gm20_rx_byte, 1);
        HAL_Delay(50);  // Reduced delay: 100ms -> 50ms

        // Read GM20 device serial number
        char sn_buffer[32];
        memset(sn_buffer, 0, sizeof(sn_buffer));

        if (GM20_GetSN(sn_buffer) == GM20_OK) {
            printf("GM20 SN read successfully: %s\r\n", sn_buffer);
            return HAL_OK;
        } else {
            return HAL_ERROR;
        }
    }
    return HAL_OK;
}

// GM20 module power on
void GM20Module_PowerOn(void)
{
    RF_PWR_ON;
    HAL_Delay(500);  // Reduced delay: 1000ms -> 500ms
}

// GM20 module power off
void GM20Module_PowerOff(void)
{
//    RF_PWR_OFF;
}

// GM20 module wakeup
HAL_StatusTypeDef GM20Module_WakeUp(void)
{
    // GM20 power and LPUART1 have been re-initialized in PowerModule_EnablePeripherals(), wait for stabilization
    HAL_Delay(300);  // Reduced delay: 500ms -> 300ms

    if (GM20_Wake() == GM20_OK) {
        printf("GM20 wakeup OK\r\n");
        return HAL_OK;
    } else {
        return HAL_ERROR;
    }
}

// GM20 module send data
HAL_StatusTypeDef GM20Module_SendData(void)
{
    // Data synthesis and transmission
    char data_string[300];
    memset(data_string, 0, sizeof(data_string));

    if (DataModule_CreatePacket(data_string, sizeof(data_string)) == HAL_OK) {
        if (DataModule_SendToGM20(data_string) == HAL_OK) {
            return HAL_OK;
        } else {
            return HAL_ERROR;
        }
    } else {
        return HAL_ERROR;
    }
}

// Create data packet
HAL_StatusTypeDef DataModule_CreatePacket(char *output_buffer, uint16_t buffer_size)
{
    return Create_Data_String(output_buffer, buffer_size);
}

// Send data to GM20
HAL_StatusTypeDef DataModule_SendToGM20(const char *data_string)
{
    return Send_Data_To_GM20(data_string);
}

// Print current time
void SystemModule_PrintCurrentTime(void)
{
    RTC_TimeTypeDef current_time;
    RTC_DateTypeDef current_date;
    HAL_RTC_GetTime(&hrtc, &current_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_date, RTC_FORMAT_BIN);
    printf("Current RTC time: %02d:%02d:%02d %02d/%02d/%02d\r\n",
           current_time.Hours, current_time.Minutes, current_time.Seconds,
           current_date.Date, current_date.Month, current_date.Year);
}

// GM20 module smart wait: monitor signal strength, continue working when signal present, shutdown immediately when signal disappears
void GM20Module_SmartWait(void)
{
    uint32_t wait_start_time = HAL_GetTick();
    uint32_t last_signal_check = wait_start_time;
    uint8_t signal_detected = 0;
    int8_t signal_quality = -128;
    uint16_t pending_count = 0;
    uint8_t status_printed = 0;  // Flag to mark if status has been output

    // Signal detection interval (milliseconds)
    const uint32_t SIGNAL_CHECK_INTERVAL = 5000; // Check signal every 5 seconds
    const uint32_t INITIAL_WAIT_TIME = GM20_WAIT_TIME_SECONDS * 1000; // Base wait time

    while (1) {
        uint32_t current_time = HAL_GetTick();

        // Periodically check signal strength
        if ((current_time - last_signal_check) >= SIGNAL_CHECK_INTERVAL) {
            last_signal_check = current_time;

            // Query signal quality and pending data count
            if (GM20_GetSignalQuality(&signal_quality) == GM20_OK &&
                GM20_QueryDataCount(&pending_count) == GM20_OK) {

                // Only output status information on first time
                if (!status_printed) {
                    printf("Pending data count: %d\r\n", pending_count);
                    status_printed = 1;
                }

                if (signal_quality != -128) {
                    // Signal detected
                    if (!signal_detected) {
                        signal_detected = 1;
                    }
                    // Reset wait start time, continue working
                    wait_start_time = current_time;
                } else {
                    // No signal
                    if (signal_detected) {
                        return; // Exit immediately after signal disappears, enter sleep
                    }
                }
            }
        }

        // Check if should exit wait (only in no signal state)
        if (!signal_detected) {
            uint32_t elapsed_time = current_time - wait_start_time;
            if (elapsed_time >= INITIAL_WAIT_TIME) {
                break;
            }
        }

        // Brief delay to avoid excessive CPU usage
        HAL_Delay(1000);
    }
}

// System enter sleep mode
void SystemModule_EnterSleepMode(void)
{
    // Disable peripheral power
    PowerModule_DisablePeripherals();

    // Print time before sleep
    RTC_TimeTypeDef time_before_sleep;
    RTC_DateTypeDef date_before_sleep;
    HAL_RTC_GetTime(&hrtc, &time_before_sleep, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &date_before_sleep, RTC_FORMAT_BIN);
    printf("Time before sleep: %02d:%02d:%02d\r\n",
           time_before_sleep.Hours, time_before_sleep.Minutes, time_before_sleep.Seconds);

    // Use macro-defined sleep time
    printf("Sleep for %d seconds\r\n", SLEEP_DURATION_SECONDS);

    // Update sleep time configuration
    wakeup_counter = SLEEP_DURATION_SECONDS - 1;

    // Enter low power mode
    EnterStopMode();
}

// Battery voltage protection module implementation

// Check battery voltage and manage protection status
uint8_t BatteryProtection_CheckVoltage(void)
{
    extern float pw; // Current battery voltage

    if (low_voltage_protection_active) {
        // Currently in low voltage protection state, check if recovery is possible
        if (pw > BATTERY_RECOVERY_VOLTAGE_THRESHOLD) {
            // Voltage recovered, exit low voltage protection mode
            BatteryProtection_ExitLowVoltageMode();
            return 2; // Exit low voltage protection mode
        } else {
            // Voltage still too low, continue protection mode
            printf("Battery protection active: %.3fV < %.1fV\r\n",
                   pw, BATTERY_RECOVERY_VOLTAGE_THRESHOLD);
            return 1; // Continue low voltage protection mode
        }
    } else {
        // Currently in normal working state, check if need to enter protection mode
        if (pw < BATTERY_LOW_VOLTAGE_THRESHOLD) {
            // Voltage too low, enter low voltage protection mode
            BatteryProtection_EnterLowVoltageMode();
            return 1; // Enter low voltage protection mode
        } else {
            // Voltage normal, continue normal operation
            return 0; // Normal working mode
        }
    }
}

// Enter low voltage protection mode
void BatteryProtection_EnterLowVoltageMode(void)
{
    extern float pw; // Current battery voltage

    printf("*** LOW VOLTAGE PROTECTION ACTIVATED ***\r\n");
    printf("Battery voltage: %.3fV < %.1fV (threshold)\r\n",
           pw, BATTERY_LOW_VOLTAGE_THRESHOLD);
    printf("Shutting down GM20 module to save power\r\n");

    // Turn off GM20 module power to save power
    RF_PWR_OFF;

    // Set low voltage protection flag
    low_voltage_protection_active = 1;

    printf("Device entering low power protection mode\r\n");
}

// Exit low voltage protection mode
void BatteryProtection_ExitLowVoltageMode(void)
{
    extern float pw; // Current battery voltage

    printf("*** VOLTAGE RECOVERY DETECTED ***\r\n");
    printf("Battery voltage: %.3fV > %.1fV (recovery threshold)\r\n",
           pw, BATTERY_RECOVERY_VOLTAGE_THRESHOLD);
    printf("Restarting GM20 module\r\n");

    // Re-enable GM20 module power
    RF_PWR_ON;
    HAL_Delay(1000); // Wait for GM20 module to start and stabilize

    // Re-initialize GM20 module communication
    __HAL_RCC_LPUART1_CLK_ENABLE();
    MX_LPUART1_UART_Init();
    HAL_UART_Receive_IT(&hlpuart1, &gm20_rx_byte, 1);

    // Clear low voltage protection flag
    low_voltage_protection_active = 0;

    printf("Device resuming normal operation\r\n");
}

/* USER CODE END Application */

